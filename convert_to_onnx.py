"""
将MiDaS最大模型(dpt_beit_large_512)转换为ONNX格式
参考: https://blog.csdn.net/zhangqian_1/article/details/140291694
"""
import os
import torch
import numpy as np
from midas.model_loader import load_model

def convert_midas_to_onnx():
    """将MiDaS最大模型转换为ONNX格式"""
    
    print("开始转换MiDaS最大模型到ONNX...")
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 模型配置
    model_type = "dpt_beit_large_512"
    model_path = "weights/dpt_beit_large_512.pt"
    
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        print(f"错误: 模型文件 {model_path} 不存在!")
        print("请先下载模型权重文件")
        return False
    
    # 加载模型
    print("加载模型...")
    model, transform, net_w, net_h = load_model(
        device=device,
        model_path=model_path,
        model_type=model_type,
        optimize=False,  # 转换ONNX时不优化
        height=None,
        square=False
    )
    
    # 设置模型为评估模式
    model.eval()
    
    # 创建虚拟输入 - 根据博客参考，使用512x512分辨率
    print(f"创建虚拟输入: {net_w}x{net_h}")
    dummy_input = torch.randn(1, 3, net_h, net_w).to(device)
    
    # 输入输出名称
    input_names = ["input"]
    output_names = ["output"]
    
    # ONNX文件路径
    onnx_path = f"weights/{model_type}.onnx"
    
    print("开始导出ONNX...")
    print(f"输入尺寸: {dummy_input.shape}")
    print(f"输出路径: {onnx_path}")
    
    try:
        # 导出ONNX模型
        torch.onnx.export(
            model,
            dummy_input,
            onnx_path,
            verbose=True,
            input_names=input_names,
            output_names=output_names,
            opset_version=12,  # 使用opset 12，兼容性较好
            do_constant_folding=True,  # 常量折叠优化
            dynamic_axes={
                'input': {0: 'batch_size'},    # 批次大小可变
                'output': {0: 'batch_size'}    # 批次大小可变
            }
        )
        
        print(f"✅ ONNX模型导出成功: {onnx_path}")
        
        # 检查导出的文件
        if os.path.exists(onnx_path):
            file_size = os.path.getsize(onnx_path) / (1024 * 1024)  # MB
            print(f"文件大小: {file_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ ONNX导出失败: {str(e)}")
        return False

def test_onnx_model():
    """测试导出的ONNX模型"""
    try:
        import onnx
        import onnxruntime as ort
        
        onnx_path = "weights/dpt_beit_large_512.onnx"
        
        if not os.path.exists(onnx_path):
            print("ONNX模型文件不存在，请先运行转换")
            return False
        
        print("验证ONNX模型...")
        
        # 加载并验证ONNX模型
        onnx_model = onnx.load(onnx_path)
        onnx.checker.check_model(onnx_model)
        print("✅ ONNX模型验证通过")
        
        # 创建推理会话
        print("创建ONNX Runtime推理会话...")
        ort_session = ort.InferenceSession(onnx_path)
        
        # 获取输入输出信息
        input_info = ort_session.get_inputs()[0]
        output_info = ort_session.get_outputs()[0]
        
        print(f"输入名称: {input_info.name}")
        print(f"输入形状: {input_info.shape}")
        print(f"输入类型: {input_info.type}")
        print(f"输出名称: {output_info.name}")
        print(f"输出形状: {output_info.shape}")
        print(f"输出类型: {output_info.type}")
        
        # 测试推理
        print("测试ONNX推理...")
        test_input = np.random.randn(1, 3, 512, 512).astype(np.float32)
        outputs = ort_session.run(None, {input_info.name: test_input})
        
        print(f"✅ ONNX推理测试成功")
        print(f"输出形状: {outputs[0].shape}")
        print(f"输出数据类型: {outputs[0].dtype}")
        
        return True
        
    except ImportError:
        print("⚠️  需要安装onnx和onnxruntime来验证模型:")
        print("pip install onnx onnxruntime")
        return False
    except Exception as e:
        print(f"❌ ONNX模型验证失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("MiDaS最大模型ONNX转换工具")
    print("=" * 60)
    
    # 转换模型
    success = convert_midas_to_onnx()
    
    if success:
        print("\n" + "=" * 60)
        print("开始验证ONNX模型...")
        test_onnx_model()
    
    print("\n" + "=" * 60)
    print("转换完成!")
